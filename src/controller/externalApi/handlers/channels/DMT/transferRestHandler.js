const _ = require('lodash')

const TransferController = require('../../../channels/DMT/transfersController')
const errorMsg = require('../../../../../util/error')
const log = require('../../../../../util/log')
const ValidationError = require('../../../../../errors/ValidationError')
const { DMT_TRANSFER_MODES } = require('../../../../../model/externalApiEnum')
const { transferMode } = require('../../../../../util/util')
const Joi = require('joi')
class TransferRestHandler {
  /**
   *
   * @param {{sessionRQ:string,affiliate_id:number,amount:number,ma_user_id:number,userid:number,ma_beneficiaries_id:number,uic:string,transfer_mode:string}} fields
   * @returns
   */
  static async transfers (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'request', fields })
    try {
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        ma_beneficiaries_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'ma_beneficiaries_id is required field and must be number')),
        amount: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'amount is required field and must be number')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string')),
        transfer_mode: Joi.string().required().error(new ValidationError(2001, 'transfer_mode is required field and must be string')),
        transaction_id: Joi.string().alphanum().min(5).max(15).required().error(new ValidationError(2001, 'transaction_id is required field,must be alphanumeric,min 5 and max 15 in length')),
        beneficiary_name: Joi.string().optional().error(new ValidationError(2001, 'beneficiary_name must be string'))
      })
      await schema.validateAsync(fields)

      if (!transferMode.includes(fields.transfer_mode)) {
        throw new ValidationError(2001, 'Invalid transfer mode')
      }

      /* delete key */
      delete fields.merchant_id

      return await TransferController.transfers(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transfers', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }

  /**
   *
   * @param {{affiliate_id:number,ma_user_id:number,userid:number,order_id:string}} fields
   * @returns
   */
  static async transferStatus (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'transferStatus', type: 'request', fields })
    try {
      const schema = Joi.object({
        affiliate_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'affiliate_id is required field and must be number')),
        ipAddress: Joi.string().required().error(new ValidationError(2001, 'ipAddress is required field and must be string')),
        ma_user_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        merchant_id: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'merchant_id is required field and must be number')),
        userid: Joi.number().options({ convert: false }).integer().required().error(new ValidationError(2001, 'userid is required field and must be number')),
        transaction_id: Joi.string().required().error(new ValidationError(2001, 'transaction_id is required field and must be string')),
        uic: Joi.string().required().error(new ValidationError(2001, 'uic is required field and must be string')),
        sessionRQ: Joi.string().required().error(new ValidationError(2001, 'sessionRQ is required field and must be string'))
      })
      await schema.validateAsync(fields)

      /* delete key */
      delete fields.merchant_id

      return await TransferController.transferStatus(fields)
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'transferStatus', type: 'catcherror', fields: error })
      const response_json = { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
      if (error instanceof ValidationError) {
        response_json.message = error.message
        response_json.respcode = error.code
      }
      return response_json
    }
  }
}

module.exports = TransferRestHandler
